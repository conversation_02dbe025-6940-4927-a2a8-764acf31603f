#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/bin/node_modules:/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/node_modules:/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/prettier@3.6.2/node_modules:/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/bin/node_modules:/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/node_modules:/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/prettier@3.6.2/node_modules:/Users/<USER>/go/src/github.com/OmniOrigin/OmniParse/video/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../prettier@3.6.2/node_modules/prettier/bin/prettier.cjs" "$@"
else
  exec node  "$basedir/../../../../../prettier@3.6.2/node_modules/prettier/bin/prettier.cjs" "$@"
fi
